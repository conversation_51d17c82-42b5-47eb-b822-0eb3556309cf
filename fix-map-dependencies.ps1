# 修复地图依赖的PowerShell脚本

Write-Host "正在修复React Leaflet依赖兼容性问题..." -ForegroundColor Green

# 进入前端目录
Set-Location frontend

# 删除node_modules和lock文件以确保干净安装
Write-Host "清理现有依赖..." -ForegroundColor Yellow
if (Test-Path "node_modules") {
    Remove-Item -Recurse -Force node_modules
}
if (Test-Path "pnpm-lock.yaml") {
    Remove-Item pnpm-lock.yaml
}

# 安装兼容的react-leaflet版本
Write-Host "安装兼容的react-leaflet版本..." -ForegroundColor Yellow
pnpm install react-leaflet@4.2.1

# 重新安装所有依赖
Write-Host "重新安装所有依赖..." -ForegroundColor Yellow
pnpm install

Write-Host "依赖修复完成！" -ForegroundColor Green
Write-Host "现在可以运行 'pnpm run dev' 来启动开发服务器" -ForegroundColor Cyan

# 返回根目录
Set-Location ..